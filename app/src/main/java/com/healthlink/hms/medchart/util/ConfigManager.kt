package com.healthlink.hms.medchart.util

import android.content.Context
import android.content.SharedPreferences
import androidx.security.crypto.EncryptedSharedPreferences
import androidx.security.crypto.MasterKey
import com.healthlink.hms.medchart.BuildConfig

/**
 * 安全的配置管理器，用于存储和读取敏感信息如API密钥
 */
class ConfigManager private constructor(context: Context) {

    companion object {
        private const val PREF_NAME = "hms_secure_prefs"
        private const val KEY_API_KEY = "api_key"
        private const val DEFAULT_API_KEY = "YOUR_API_KEY" // 开发期间的默认值，生产环境应移除
        private const val TOKEN_KEY = "token"
        private const val DEFAULT_TOKEN_KEY = 13693401988
        private const val KEY_CHAT_ID = "chat_id" // 新增chatId的键

        @Volatile
        private var instance: ConfigManager? = null

        fun getInstance(context: Context): ConfigManager {
            return instance ?: synchronized(this) {
                instance ?: ConfigManager(context.applicationContext).also { instance = it }
            }
        }
    }

    private val masterKey = MasterKey.Builder(context)
        .setKeyScheme(MasterKey.KeyScheme.AES256_GCM)
        .build()

    private val securePreferences: SharedPreferences = if (BuildConfig.DEBUG) {
        // 开发环境使用普通SharedPreferences方便调试
        context.getSharedPreferences(PREF_NAME, Context.MODE_PRIVATE)
    } else {
        // 生产环境使用加密的SharedPreferences
        EncryptedSharedPreferences.create(
            context,
            PREF_NAME,
            masterKey,
            EncryptedSharedPreferences.PrefKeyEncryptionScheme.AES256_SIV,
            EncryptedSharedPreferences.PrefValueEncryptionScheme.AES256_GCM
        )
    }

    /**
     * 设置token
     */
    fun setToken(token: Long) {
        securePreferences.edit().putLong(TOKEN_KEY, token).apply()
    }

    /**
     * 获取token
     */
    fun getToken(): Long? {
        return securePreferences.getLong(TOKEN_KEY, DEFAULT_TOKEN_KEY)
    }

    /**
     * 获取API密钥，如果没有设置，则返回默认值
     */
    fun getApiKey(): String {
        return securePreferences.getString(KEY_API_KEY, DEFAULT_API_KEY) ?: DEFAULT_API_KEY
    }

    /**
     * 设置API密钥
     */
    fun setApiKey(apiKey: String) {
        securePreferences.edit().putString(KEY_API_KEY, apiKey).apply()
    }

    /**
     * 检查API密钥是否已设置（不是默认值）
     */
    fun isApiKeySet(): Boolean {
        return getApiKey() != DEFAULT_API_KEY
    }

    /**
     * 获取存储的chatId
     */
    fun getChatId(): String? {
        return securePreferences.getString(KEY_CHAT_ID, "")
    }

    /**
     * 设置chatId
     */
    fun setChatId(chatId: String) {
        securePreferences.edit().putString(KEY_CHAT_ID, chatId).apply()
    }

    /**
     * 清除chatId
     */
    fun clearChatId() {
        securePreferences.edit().remove(KEY_CHAT_ID).apply()
    }
}