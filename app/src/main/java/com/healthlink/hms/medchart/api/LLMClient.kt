package com.healthlink.hms.medchart.api

import android.content.Context
import android.util.Log
import okhttp3.Interceptor
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.Response
import okhttp3.logging.HttpLoggingInterceptor
import retrofit2.Retrofit
import retrofit2.converter.gson.GsonConverterFactory
import java.security.SecureRandom
import java.security.cert.X509Certificate
import java.util.concurrent.TimeUnit
import javax.net.ssl.SSLContext
import javax.net.ssl.TrustManager
import javax.net.ssl.X509TrustManager

class LLMClient private constructor(context: Context) {
    
    private val loggingInterceptor = HttpLoggingInterceptor().apply {
        level = HttpLoggingInterceptor.Level.BODY
    }
    
    // 自定义拦截器，用于打印请求参数和响应
    private val customInterceptor = Interceptor { chain ->
        val request = chain.request()
        val requestBuilder = request.newBuilder()
        
        // ===== 请求日志 =====
        Log.d(TAG, "┌────── 请求信息 ──────")
        Log.d(TAG, "│ 请求URL: ${request.url}")
        Log.d(TAG, "│ 请求方法: ${request.method}")
        
        // 打印请求头
        Log.d(TAG, "│ 请求头:")
        request.headers.forEach { 
            Log.d(TAG, "│ - ${it.first}: ${it.second}")
        }
        
        // 打印请求体
        request.body?.let {
            if (request.header("Content-Type")?.contains("application/json") == true) {
                it.contentLength().let { length ->
                    if (length > 0) {
                        val buffer = okio.Buffer()
                        it.writeTo(buffer)
                        val requestBody = buffer.readUtf8()
                        Log.d(TAG, "│ 请求体: $requestBody")
                    }
                }
            }
        }
        Log.d(TAG, "└─────────────────────")
        
        // 执行请求
        val startTime = System.currentTimeMillis()
        val response = chain.proceed(requestBuilder.build())
        val duration = System.currentTimeMillis() - startTime
        
        // ===== 响应日志 =====
        Log.d(TAG, "┌────── 响应信息 ──────")
        Log.d(TAG, "│ 响应URL: ${response.request.url}")
        Log.d(TAG, "│ 响应状态: ${response.code} - ${response.message}")
        Log.d(TAG, "│ 响应时间: ${duration}ms")
        
        // 打印响应头
        Log.d(TAG, "│ 响应头:")
        response.headers.forEach {
            Log.d(TAG, "│ - ${it.first}: ${it.second}")
        }
        
        // 打印响应体 (注意这部分比较复杂，因为要确保response.body不被消费)
        val responseBody = response.body
        if (responseBody != null) {
            val contentType = responseBody.contentType()
            val contentLength = responseBody.contentLength()
            
            if (contentType != null && contentLength != 0L && responseBody.contentLength() < 1024 * 1024) {
                // 克隆一个响应体用于读取
                val source = responseBody.source()
                source.request(Long.MAX_VALUE)
                val buffer = source.buffer.clone()
                val bodyString = buffer.readUtf8()
                
                if (bodyString.isNotEmpty()) {
                    Log.d(TAG, "│ 响应体: $bodyString")
                }
            } else {
                Log.d(TAG, "│ 响应体: [二进制数据或过大，未打印]")
            }
        }
        
        Log.d(TAG, "└─────────────────────")
        
        response
    }
    
    // 创建信任所有证书的TrustManager (在生产环境中，应使用正确的证书验证)
    private val trustAllCerts = arrayOf<TrustManager>(object : X509TrustManager {
        override fun checkClientTrusted(chain: Array<out X509Certificate>?, authType: String?) {}
        override fun checkServerTrusted(chain: Array<out X509Certificate>?, authType: String?) {}
        override fun getAcceptedIssuers(): Array<X509Certificate> = arrayOf()
    })
    
    // 配置SSL
    private val sslContext = SSLContext.getInstance("TLS").apply {
        init(null, trustAllCerts, SecureRandom())
    }
    
    private val okHttpClient = OkHttpClient.Builder()
        .addInterceptor(loggingInterceptor)
        .addInterceptor(customInterceptor) // 添加自定义拦截器
        .connectTimeout(60, TimeUnit.SECONDS)
        .readTimeout(60, TimeUnit.SECONDS)
        .writeTimeout(60, TimeUnit.SECONDS)
        .sslSocketFactory(sslContext.socketFactory, trustAllCerts[0] as X509TrustManager)
        .hostnameVerifier { _, _ -> true } // 不验证主机名
        .build()
    
    private val retrofit = Retrofit.Builder()
        .baseUrl(BASE_URL)
        .client(okHttpClient)
        .addConverterFactory(GsonConverterFactory.create())
        .build()
    
    val llmApiService: LLMApiService = retrofit.create(LLMApiService::class.java)
    
    companion object {
        private const val BASE_URL = "https://demo-test.healthlinkiot.com/"
        private const val TAG = "LLMClient" // 日志标签
        
        @Volatile
        private var instance: LLMClient? = null
        
        fun getInstance(context: Context): LLMClient {
            return instance ?: synchronized(this) {
                instance ?: LLMClient(context.applicationContext).also { instance = it }
            }
        }
    }
}