package com.healthlink.hms.medchart

import android.app.Application
import android.content.Context
import android.util.Log

class HMSApplication : Application() {

    companion object {
        private const val TAG = "HMSApplication"
        lateinit var appContext: Context
    }

    override fun onCreate() {
        super.onCreate()
        Log.d(TAG, "Application started")
        appContext = applicationContext
        
        // 在这里进行全局初始化
        // 例如: 配置加密工具、初始化第三方SDK等
    }
}