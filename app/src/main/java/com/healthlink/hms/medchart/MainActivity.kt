package com.healthlink.hms.medchart

import android.os.Bundle
import android.util.Log
import android.view.Menu
import android.view.MenuItem
import android.view.MotionEvent
import android.view.View
import android.view.WindowManager
import android.view.inputmethod.InputMethodManager
import android.widget.Toast
import androidx.activity.enableEdgeToEdge
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import androidx.core.view.ViewCompat
import androidx.core.view.WindowInsetsCompat
import androidx.recyclerview.widget.LinearLayoutManager
import com.healthlink.hms.medchart.databinding.ActivityMainBinding
import com.healthlink.hms.medchart.ui.ChatAdapter
import com.healthlink.hms.medchart.viewmodel.LLMViewModel

class MainActivity : AppCompatActivity() {

    private lateinit var binding: ActivityMainBinding
    private val viewModel: LLMViewModel by viewModels()
    private lateinit var chatAdapter: ChatAdapter
    // 记录输入区域的初始位置
    private var inputLayoutOriginalY = 0f
    private var isKeyboardVisible = false


    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()
        binding = ActivityMainBinding.inflate(layoutInflater)
        setContentView(binding.root)

        setupRecyclerView()

        // 设置点击监听和键盘监听
        setupListeners()

        observeViewModel()

        // 执行登录操作
        if(viewModel.loginStatus.value == false) {
            viewModel.login()
        }

        // 布局完成后记录输入区域的初始位置
        binding.rootLayout.post {
            inputLayoutOriginalY = binding.inputLayout.y
        }
    }

    private fun setupListeners() {

        // 监听输入框获取焦点事件
        binding.messageInput.setOnFocusChangeListener { _, hasFocus ->
            if (hasFocus) {
                // 当输入框获取焦点时，记录键盘已显示
                isKeyboardVisible = true
            }
        }

        // 监听消息列表的点击事件
        binding.chatRecyclerView.setOnTouchListener { _, event ->
            if (event.action == MotionEvent.ACTION_DOWN) {
                // 点击聊天区域，隐藏键盘，重置输入区域位置
                hideKeyboardAndResetInputLayout()
                // 清除输入框焦点
                binding.messageInput.clearFocus()
            }
            false
        }

        // 监听发送按钮点击事件
        binding.sendButton.setOnClickListener {
            val message = binding.messageInput.text.toString().trim()
            if (message.isNotEmpty()) {
                // 确保使用流式API (chat3接口)
                viewModel.sendMessage(message, true) // 强制使用流式API
                binding.messageInput.text?.clear()
            } else {
                // 提示用户输入内容
                Toast.makeText(this, "请输入内容", Toast.LENGTH_SHORT).show()
            }
        }
    }

    private fun hideKeyboardAndResetInputLayout() {
        if (isKeyboardVisible) {
            // 隐藏键盘
            val imm = getSystemService(INPUT_METHOD_SERVICE) as InputMethodManager
            imm.hideSoftInputFromWindow(binding.messageInput.windowToken, 0)
            Log.i(TAG,"binding.messageInput.text ${binding.messageInput.text}")
            if (binding.messageInput.text == null || binding.messageInput.text.toString().trim().isEmpty()) {
                binding.messageInput.text?.clear()
            }
        }
    }



    private fun setupRecyclerView() {
        chatAdapter = ChatAdapter()
        binding.chatRecyclerView.apply {
            layoutManager = LinearLayoutManager(this@MainActivity).apply {
                stackFromEnd = true
            }
            adapter = chatAdapter
        }
    }


    private fun observeViewModel() {
        viewModel.chatHistory.observe(this) { messages ->
            chatAdapter.submitList(messages)
            if (messages.isNotEmpty()) {
                binding.chatRecyclerView.smoothScrollToPosition(messages.size - 1)
            }
        }

        viewModel.isLoading.observe(this) { isLoading ->
            binding.progressIndicator.visibility = if (isLoading) View.VISIBLE else View.GONE
            binding.sendButton.isEnabled = !isLoading
        }

        viewModel.errorMessage.observe(this) { errorMessage ->
            errorMessage?.let {
                Toast.makeText(this, it, Toast.LENGTH_LONG).show()
            }
        }
        
        viewModel.loginStatus.observe(this) { isLoggedIn ->
            binding.sendButton.isEnabled = isLoggedIn && viewModel.isLoading.value != true
            if (isLoggedIn) {
                Toast.makeText(this, "登录成功", Toast.LENGTH_SHORT).show()
            }
        }
        
        viewModel.isStreaming.observe(this) { isStreaming ->
            // 当流式状态改变时可以更新UI
            if (isStreaming) {
                // 正在使用流式API
                binding.progressIndicator.visibility = View.VISIBLE
            }
        }
    }
    
    override fun onCreateOptionsMenu(menu: Menu): Boolean {
        menuInflater.inflate(R.menu.main_menu, menu)
        // 开关选项不再需要，因为我们总是使用流式API
        return true
    }
    
    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        return when (item.itemId) {
            R.id.action_clear -> {
                viewModel.clearChat()
                true
            }
            else -> super.onOptionsItemSelected(item)
        }
    }

    companion object {
        private const val TAG = "MainActivity"
    }
}