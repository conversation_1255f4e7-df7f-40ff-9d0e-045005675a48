# 医疗AI助手 - Android应用

这是一个使用Kotlin开发的Android应用，通过API接入大语言模型（主要使用OpenAI的GPT-4o模型）实现医疗咨询对话功能。

## 功能特点

- 与AI大模型进行医疗相关的自然语言对话
- 美观的聊天界面
- 安全存储API密钥
- 支持对话历史查看和清除
- 专注于医疗领域的回答

## 项目结构

```
com.healthlink.hms.medchart/
├── api/ - API通信相关类
│   ├── LLMApiService.kt - Retrofit API接口
│   ├── LLMClient.kt - API客户端
│   └── 数据模型类
├── repository/ - 数据仓库层
│   └── LLMRepository.kt - 大模型交互逻辑
├── ui/ - UI组件
│   └── ChatAdapter.kt - 聊天列表适配器
├── util/ - 工具类
│   └── ConfigManager.kt - 配置管理（API密钥等）
├── viewmodel/ - ViewModel层
│   └── LLMViewModel.kt - 处理UI和数据交互
├── HMSApplication.kt - 应用程序类
└── MainActivity.kt - 主Activity
```

## 准备工作

在运行此应用前，您需要：

1. 获取OpenAI API密钥（https://platform.openai.com/）
2. 将API密钥设置到应用程序中（首次启动应用时会提示）

## 如何运行

1. 使用Android Studio打开项目
2. 等待Gradle同步完成
3. 连接Android设备或启动模拟器
4. 点击运行按钮（或按下Shift+F10）

## 技术栈

- Kotlin编程语言
- AndroidX组件库
- Material Design 3 UI
- MVVM架构模式
- Retrofit + OkHttp: 网络请求
- Kotlin Coroutines: 异步处理
- ViewModel + LiveData: 数据和UI分离
- EncryptedSharedPreferences: 安全存储

## 注意事项

- API调用可能会产生费用，请查阅OpenAI的计费规则
- 默认使用的是OpenAI的GPT-4o模型，您也可以通过修改LLMRepository.kt中的MODEL参数来使用其他模型
- 应用提供的医疗建议仅供参考，重要医疗决策请咨询专业医生

## 未来功能

- 支持图像上传和分析
- 多轮对话优化
- 医疗专业术语解释
- 保存和导出对话历史
- 本地缓存减少API调用
