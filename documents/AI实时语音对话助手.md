# AI 实时语音对话助手实现方案（v2.7 资深版）

## 1. 功能与非功能目标

### 1.1. 功能目标
- **核心对话流程**: 进入聊天界面后，**自动持续监听**用户语音，实现完整的“用户语音输入(ASR) -> AI大模型处理 -> AI文本与语音回复(TTS)”闭环。
- **语音打断 (Barge-in)**: 在TTS语音播放时，用户可以随时说话并打断当前播放，系统应立即响应用户的输入。
- **内置播放能力**: **强制要求**使用 `Android TextToSpeech` 的内置播放能力，不允许引入独立的播放器组件，以保持应用的轻量化。
- **连续对话**: 支持基于上下文的多轮连续对话。
- **欢迎词**: App首次进入或新会话开始时，自动播报欢迎与引导词。

### 1.2. 非功能目标
- **响应性**: 从用户停止说话到AI开始语音回复，端到端延迟在理想网络下应低于3秒。对于语音打断，响应应更迅速。
- **健壮性**: 在网络异常、权限缺失、TTS/ASR引擎初始化失败等场景下，应用应有明确的提示和优雅的降级策略。
- **资源占用**: 优化麦克风和网络使用，在不使用时及时释放资源，减少电量消耗。
- **可维护性**: 采用业界推荐的架构模式（如MVVM），代码结构清晰，易于扩展和测试。

---

## 2. 整体架构设计

### 2.1. 架构模式 (MVVM)
我们将采用**MVVM (Model-View-ViewModel)** 架构，结合**Repository**模式，实现UI、业务逻辑和数据源的彻底分离。

```mermaid
graph TD
    subgraph View
        A[ChatActivity/Compose Screen]
    end
    subgraph ViewModel
        B[ChatViewModel]
    end
    subgraph Model
        C[DialogueRepository]
        D[ASRManager]
        E[TTSManager]
        F[LLMService]
        G[PersistenceDataSource]
    end

    A -- "进入界面自动监听" --> B
    B -- "更新UI State (如:显示'正在倾听')" --> A
    
    B -- "启动/停止录音" --> D
    D -- "识别结果(String)" --> B
    
    B -- "发送问题文本" --> C
    C -- "调用大模型API" --> F
    F -- "返回AI回复" --> C
    C -- "历史记录" --> G
    
    C -- "AI回复(String)" --> B
    B -- "请求播放" --> E
    E -- "播放语音" --> H(( ))
    style H display:none
    
    B -- "更新UI State(如:新增消息)" --> A
```

### 2.2. 系统架构流程图

```mermaid
graph TD
    subgraph "UI Layer"
        A[ChatActivity / Compose Screen]
    end

    subgraph "ViewModel Layer"
        B[ChatViewModel]
    end

    subgraph "Data Layer"
        C[DialogueRepository]
        F[LLMService (Retrofit)]
        G[PersistenceDataSource (Room)]
    end

    subgraph "Manager / OS Layer"
        D[ASRManager / SpeechRecognizer]
        E[TTSManager / TextToSpeech]
    end

    subgraph "External / System"
        H[AI Service (Network)]
        I[Local Database (SQLite)]
        J[Android OS (Audio In/Out)]
    end

    %% Data Flow
    A -- "1. User Interaction / Lifecycle" --> B
    B -- "2. Observe UiState" --> A

    B -- "3. Start/Stop Listening" --> D
    D -- "OS Audio Input" --> J
    J -- "Audio Stream" --> D
    D -- "4. onResult(text)" --> B

    B -- "5. processRequest(text)" --> C
    C -- "6. fetchReply(text)" --> F
    F -- "7. API Call" --> H
    H -- "8. API Response" --> F
    C -- "9. saveMessage(message)" --> G
    G -- "10. Write to DB" --> I
    C -- "11. return reply" --> B

    B -- "12. speak(text)" --> E
    E -- "OS Audio Output" --> J
    E -- "13. onDone()" --> B

    B -- "14. Update UiState" --> B
```

### 2.3. 核心组件职责
- **View (Activity/Compose)**: 仅负责UI渲染，在生命周期事件（如`onResume`）中通知`ViewModel`开始监听。观察`ViewModel`的UI状态并更新界面。
- **ChatViewModel**: 核心业务逻辑处理中心。管理对话状态（`UiState`），协调ASR、TTS和Repository工作，**处理语音打断逻辑**。
- **DialogueRepository**: 唯一数据来源。封装AI接口调用和本地数据（如聊天记录）的存取。
- **ASRManager**: 封装`SpeechRecognizer`，负责**持续监听**、语音转文本，并处理`RECORD_AUDIO`权限。
- **TTSManager**: 封装`TextToSpeech`，负责TTS引擎的初始化、语音合成、播放控制和**音频焦点管理**。
- **LLMService**: 使用Retrofit定义的接口，负责与AI大模型API进行网络通信。
- **PersistenceDataSource**: 使用 **Room** 实现聊天记录的本地持久化，以支持结构化数据的高效读写和查询。

### 2.4. UI状态管理
使用Kotlin `StateFlow` 对UI状态进行建模，确保UI的响应式和一致性。
```kotlin
data class ChatUiState(
    val messages: List<Message> = emptyList(),
    val inputState: InputState = InputState.Idle, // Idle, Listening, Processing
    val ttsState: TtsState = TtsState.Idle // Idle, Speaking
)

// Message, InputState, TtsState 等为具体的状态类
```

### 2.5. 核心对话流程图
```mermaid
graph TD
    subgraph "Conversation Loop"
        direction LR
        A(Start) --> B{"自动监听中..."};
        B --> C[用户说话];
        C --> D[ASR: 语音转文本];
        D --> E[LLM: 处理文本];
        E --> F[AI 生成回复文本];
        F --> G[TTS: 文本转语音];
        G --> H((播放AI语音回复));

        H --> B;
        H -- "用户说话打断 (Barge-in)" --> D;
    end

    subgraph "State Changes"
        B -- "UiState: Listening" --> UX1(UI: 显示聆听动画);
        D -- "UiState: Processing" --> UX2(UI: 显示处理中...);
        G -- "UiState: Speaking" --> UX3(UI: 显示说话动画);
        H -- "UiState: Listening" --> UX1;
    end

    style A fill:#e8f5e9
    style H fill:#e8f5e9
    linkStyle 8 stroke:#c0392b,stroke-width:2px,stroke-dasharray: 5 5;
```

---

## 3. 核心技术模块详解

### 3.1. 语音识别 (ASR) - ASRManager
- **技术选型**: 首选 `Android SpeechRecognizer`。它集成度高，无需额外SDK。
- **持续监听逻辑**: 实现一个循环或回调链，在`onResults`或`onError`之后，如果`ViewModel`的状态仍然是`Listening`，则自动重新启动监听。需要加入合适的延迟和错误次数限制，避免无限循环和CPU占用过高。
- **错误处理**: `onError`回调是关键。需特别处理 `ERROR_NO_MATCH` (未听到) 和 `ERROR_SPEECH_TIMEOUT` (静音超时)，在这些情况下应自动重启监听。对于 `ERROR_CLIENT` 等严重错误，则应停止监听并向用户提示。
- **权限处理**: 必须在启动识别前检查并请求 `android.permission.RECORD_AUDIO` 权限。设计完善的权限请求及被拒绝后的提示流程。
- **生命周期管理**: `ASRManager`应与`ViewModel`的生命周期绑定，在`onCleared()`时释放`SpeechRecognizer`资源。
- **线程管理**: 识别回调位于非UI线程，必须通过`viewModelScope`切换到主线程更新UI State。

### 3.2. 语音合成 (TTS) - TTSManager
- **初始化与资源管理**: `TextToSpeech`的初始化是异步的。必须在`onInit`回调成功后才能使用。在`ViewModel`的`onCleared()`中调用`tts.shutdown()`释放资源。
- **状态回调**: 通过`UtteranceProgressListener`可以精确监听 **一个具体的语音任务（utterance）** 的开始、完成和出错。这是实现TTS播放完毕后自动恢复ASR监听的最可靠方式。
- **音频焦点 (Audio Focus)**: **关键点！** 在`speak()`之前，必须请求音频焦点(`AudioManager.requestAudioFocus`)。在播放结束后，释放焦点。当焦点被其他应用抢占时，应暂停或停止播放。这是保证良好用户体验的核心。
- **播放队列**: 使用`TextToSpeech.QUEUE_FLUSH`来中断当前播报并播放新内容，适用于需要立即响应的场景。

### 3.3. AI对话接口 & 数据层
- **Repository模式**: `DialogueRepository`作为统一入口，向ViewModel屏蔽数据来源是网络还是本地缓存。
- **网络层 (Retrofit + OkHttp)**:
    - **错误处理**: 统一处理HTTP错误码和网络异常（如`NoInternetException`）。
    - **超时设置**: 为OkHttp客户端设置合理的连接、读取、写入超时。
    - **日志与拦截器**: 添加日志拦截器便于调试，以及请求头拦截器用于添加API Key。
- **API Key管理**: **严禁**将API Key硬编码在代码中。应使用`BuildConfig`字段或`secrets-gradle-plugin`从`local.properties`文件安全地注入。

### 3.4. 持久化 (Room) 详解
- **实体 (`@Entity`)**: 定义 `Message` 数据类，并使用 `@Entity` 注解。字段应包括`id`, `text`, `timestamp`, `sender` (user/ai)。
    ```kotlin
    @Entity(tableName = "messages")
    data class Message(
        @PrimaryKey(autoGenerate = true) val id: Long = 0,
        val text: String,
        val timestamp: Long,
        val sender: String
    )
    ```
- **数据访问对象 (`@Dao`)**: 定义 `MessageDao` 接口，提供 `insert`, `getAll`, `clearAll` 等方法。所有方法都应为 `suspend` 函数，以便在协程中调用。
    ```kotlin
    @Dao
    interface MessageDao {
        @Insert
        suspend fun insert(message: Message)

        @Query("SELECT * FROM messages ORDER BY timestamp DESC")
        fun getAll(): Flow<List<Message>> // 使用Flow以实现响应式加载
    }
    ```

---

## 4. 开发任务分解 (细化版)

### 里程碑 1：架构与环境搭建 (P0)
- [ ] **项目初始化**: 创建Android项目，迁移到Kotlin DSL (`build.gradle.kts`)。
- [ ] **依赖配置**: 添加 Retrofit, OkHttp, Coroutines, ViewModel, Hilt, Room 的依赖。
- [ ] **持久化配置 (Room)**:
    - [ ] 定义 `Message` @Entity。
    - [ ] 创建 `MessageDao` @Dao 接口。
    - [ ] 创建 `AppDatabase` 类继承 `RoomDatabase`。
- [ ] **架构搭建**:
    - [ ] 创建`ChatViewModel`、`DialogueRepository`。
    - [ ] 使用Hilt创建Module，提供`AppDatabase`和`MessageDao`的依赖注入。
- [ ] **UI基础**: 搭建基础聊天界面（`RecyclerView`或`Compose LazyColumn`），能显示静态消息列表。
- [ ] **API Key配置**: 使用`secrets-gradle-plugin`完成API Key的安全配置。

### 里程碑 2：核心对话流打通 (P0)
- [ ] **ASR集成**:
    - [ ] 实现`ASRManager`，封装`SpeechRecognizer`。
    - [ ] 完成`RECORD_AUDIO`权限的请求和处理流程。
    - [ ] **实现ASR持续监听循环，并在`ViewModel`中启动/停止。**
- [ ] **网络请求**:
    - [ ] 定义Retrofit接口`LLMService`。
    - [ ] 在`DialogueRepository`中实现调用AI接口的方法。
- [ ] **状态驱动UI**: 将用户问题和AI回复（此时仅文本）通过`StateFlow`驱动UI进行展示。
- [ ] **TTS集成**:
    - [ ] 实现`TTSManager`，处理异步初始化和资源释放。
    - [ ] **实现`UtteranceProgressListener`来监听播放完成事件。**
    - [ ] 在ViewModel中实现：接收到AI文本后，调用`tts.speak()`进行播放。

### 里程碑 3：健壮性与体验优化 (P1)
- [ ] **音频焦点管理**: 在`TTSManager`中完整实现`requestAudioFocus`和`abandonAudioFocus`的逻辑。
- [ ] **语音打断 (Barge-in)**: 
    - [ ] 实现基础版：当ASR获得结果时，立即调用 `tts.stop()` 中断播放。
    - [ ] **挑战**：在TTS播放时，`SpeechRecognizer` 可能无法正常工作或被TTS自己的声音干扰。如果原生API无法满足要求，需记录此问题，并考虑切换到更专业的第三方ASR SDK作为长远方案。
- [ ] **错误处理**:
    - [ ] 在`Repository`中捕获网络异常，并返回包含错误信息的结果。
    - [ ] 在`ViewModel`中处理来自ASR, TTS和Repository的错误状态，并更新UI。
- [ ] **欢迎词**: 在进入界面且TTS初始化成功后，自动播报欢迎词。

### 里程碑 4：测试与发布准备 (P2)
- [ ] **单元测试**: 
    - [ ] 为`ChatViewModel`编写JUnit单元测试，验证UI State是否根据ASR, TTS, API的返回结果正确转换。
    - [ ] 为`DialogueRepository`编写测试，使用MockWebServer模拟网络请求的成功和失败场景。
- [ ] **UI测试**: (可选)使用Espresso或Compose Test编写UI测试，验证“语音打断”和“错误提示”的核心交互流程。
- [ ] **ProGuard/R8配置**: 配置混淆规则，确保依赖库和反射调用在发布包中正常工作。

---

## 5. 关键挑战与解决方案

- **持续监听的稳定性**: `SpeechRecognizer`在识别一次后或出错后会停止。需要实现可靠的重启机制，同时处理好`onError`中的各种错误码（如`ERROR_NO_MATCH`, `ERROR_SPEECH_TIMEOUT`），避免在没有语音时频繁重启。
- **语音打断 (Barge-in) 的挑战**: 当TTS正在播报时，ASR需要同时开启才能实现打断。这带来了严峻的挑战：
    - **回声消除 (Acoustic Echo Cancellation)**: ASR引擎可能会识别到TTS自己播放的声音，造成“自我识别”的死循环。原生的`SpeechRecognizer`几乎不具备回声消除能力，这是一个**高级特性**，通常需要专门的第三方ASR SDK（如讯飞、声网等）才能有效解决。
    - **资源冲突**: 麦克风和扬声器同时工作，对系统音频管理要求更高。
    - **可靠性**: 在有背景音（即TTS播报）的情况下，`SpeechRecognizer`的识别准确率可能会显著下降。
- **TTS初始化延迟**: `TextToSpeech`初始化是异步的。在UI启动时立即发起初始化，并使用`StateFlow`来标记TTS是否就绪。在就绪前，播放请求应被缓存或UI上的播放按钮应被禁用。
- **音频焦点冲突**: 当有电话或其他媒体播放时，系统会抢占音频焦点。必须监听`AudioManager.OnAudioFocusChangeListener`，并根据焦点丢失类型（短暂或永久）来暂停或停止TTS播报。

---

## 6. 交付结果
✅ 一个工程实践优秀、体验良好、健壮可靠的AI实时语音对话助手，包含：
- **完整功能**: 实现所有核心功能。
- **高质量代码**: 遵循MVVM架构，代码清晰、可测试、易维护。
- **健壮的错误处理**: 对各种异常情况有优雅的处理和提示。
- **完善的音频管理**: 正确处理音频焦点，不干扰其他应用。
- **核心单元测试**: 保证核心业务逻辑的稳定性。
